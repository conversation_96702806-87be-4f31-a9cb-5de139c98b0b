import { defineStore } from 'pinia';
import { ref, watch, computed } from 'vue';

// Configuration constants
const MAX_HISTORY = 50; // Increased limit for better user experience
const STORAGE_KEY = 'bundleHistory';
const STORAGE_VERSION = '1.0'; // For future migration support

// Helper function to check browser storage capacity
const getStorageInfo = () => {
  try {
    const test = 'storage-test';
    localStorage.setItem(test, test);
    localStorage.removeItem(test);

    // Estimate current usage
    let currentSize = 0;
    for (let key in localStorage) {
      if (Object.prototype.hasOwnProperty.call(localStorage, key)) {
        currentSize += localStorage[key].length + key.length;
      }
    }

    return {
      available: true,
      estimatedSize: currentSize,
      maxSize: 5 * 1024 * 1024 // 5MB typical limit
    };
  } catch {
    return { available: false, estimatedSize: 0, maxSize: 0 };
  }
};

// Helper function to generate unique ID for records
const generateRecordId = () => {
  return Date.now().toString(36) + Math.random().toString(36).substring(2);
};

// Helper function to create input signature for deduplication
const createInputSignature = (input) => {
  const { appName, industry, regions, companyName, style } = input;
  return `${appName}-${industry}-${regions.sort().join(',')}-${companyName || ''}-${style}`;
};

export const useHistoryStore = defineStore('history', () => {
  // Load existing records with version check
  const loadRecords = () => {
    try {
      const stored = localStorage.getItem(STORAGE_KEY);
      if (!stored) return [];

      const data = JSON.parse(stored);

      // Handle legacy format or add missing fields
      return (data.records || data).map(record => ({
        id: record.id || generateRecordId(),
        timestamp: record.timestamp,
        input: record.input,
        results: record.results,
        signature: record.signature || createInputSignature(record.input)
      }));
    } catch (e) {
      console.warn('Failed to load history from localStorage:', e);
      return [];
    }
  };

  const records = ref(loadRecords());
  const storageInfo = ref(getStorageInfo());

  // Computed properties for analytics
  const totalGenerations = computed(() => records.value.length);
  const uniqueApps = computed(() => {
    const apps = new Set(records.value.map(r => r.input.appName.toLowerCase()));
    return apps.size;
  });
  const mostUsedIndustry = computed(() => {
    const industries = records.value.map(r => r.input.industry);
    const counts = {};
    industries.forEach(industry => {
      counts[industry] = (counts[industry] || 0) + 1;
    });
    return Object.keys(counts).reduce((a, b) => counts[a] > counts[b] ? a : b, '');
  });

  // Watch for changes and save to localStorage with error handling
  watch(records, (newVal) => {
    try {
      // Trim to max history limit
      const trimmed = newVal.slice(0, MAX_HISTORY);

      // Create storage object with metadata
      const storageData = {
        version: STORAGE_VERSION,
        lastUpdated: new Date().toISOString(),
        records: trimmed
      };

      const serialized = JSON.stringify(storageData);

      // Check if we're approaching storage limits
      if (serialized.length > storageInfo.value.maxSize * 0.8) {
        console.warn('Approaching localStorage limit, consider reducing history');
        // Auto-trim more aggressively if needed
        storageData.records = trimmed.slice(0, Math.floor(MAX_HISTORY * 0.7));
      }

      localStorage.setItem(STORAGE_KEY, JSON.stringify(storageData));

      // Update storage info
      storageInfo.value = getStorageInfo();

    } catch (e) {
      console.error('Failed to save history to localStorage:', e);
      // If storage fails, try to save a minimal version
      try {
        const minimal = newVal.slice(0, 10).map(record => ({
          timestamp: record.timestamp,
          input: { appName: record.input.appName, industry: record.input.industry },
          results: record.results.slice(0, 1)
        }));
        localStorage.setItem(STORAGE_KEY, JSON.stringify({ records: minimal }));
      } catch (e2) {
        console.error('Failed to save minimal history:', e2);
      }
    }
  }, { deep: true });

  // Add a new record with deduplication
  const addRecord = (input, results) => {
    const signature = createInputSignature(input);

    // Check for recent duplicate (within last 5 records)
    const recentDuplicate = records.value.slice(0, 5).find(record =>
      record.signature === signature
    );

    if (recentDuplicate) {
      // Update existing record instead of creating duplicate
      recentDuplicate.timestamp = new Date().toISOString();
      recentDuplicate.results = [...results];

      // Move to top
      const index = records.value.indexOf(recentDuplicate);
      records.value.splice(index, 1);
      records.value.unshift(recentDuplicate);
    } else {
      // Create new record
      const newRecord = {
        id: generateRecordId(),
        timestamp: new Date().toISOString(),
        input: { ...input },
        results: [...results],
        signature
      };

      records.value.unshift(newRecord);
    }
  };

  // Remove a specific record
  const removeRecord = (recordId) => {
    const index = records.value.findIndex(record => record.id === recordId);
    if (index > -1) {
      records.value.splice(index, 1);
    }
  };

  // Clear all records
  const clearAllRecords = () => {
    records.value.splice(0);
  };

  // Search records
  const searchRecords = (query) => {
    if (!query) return records.value;

    const lowerQuery = query.toLowerCase();
    return records.value.filter(record =>
      record.input.appName.toLowerCase().includes(lowerQuery) ||
      record.input.industry.toLowerCase().includes(lowerQuery) ||
      (record.input.companyName && record.input.companyName.toLowerCase().includes(lowerQuery)) ||
      record.results.some(result => result.toLowerCase().includes(lowerQuery))
    );
  };

  // Get records by date range
  const getRecordsByDateRange = (startDate, endDate) => {
    return records.value.filter(record => {
      const recordDate = new Date(record.timestamp);
      return recordDate >= startDate && recordDate <= endDate;
    });
  };

  // Export records for backup
  const exportRecords = () => {
    return {
      version: STORAGE_VERSION,
      exportDate: new Date().toISOString(),
      totalRecords: records.value.length,
      records: records.value
    };
  };

  // Import records from backup
  const importRecords = (importData) => {
    try {
      if (importData.records && Array.isArray(importData.records)) {
        // Merge with existing records, avoiding duplicates
        const existingSignatures = new Set(records.value.map(r => r.signature));
        const newRecords = importData.records.filter(record =>
          !existingSignatures.has(record.signature || createInputSignature(record.input))
        );

        records.value.unshift(...newRecords);
        return { success: true, imported: newRecords.length };
      }
      return { success: false, error: 'Invalid import data format' };
    } catch (e) {
      return { success: false, error: e.message };
    }
  };

  return {
    records,
    storageInfo,
    totalGenerations,
    uniqueApps,
    mostUsedIndustry,
    addRecord,
    removeRecord,
    clearAllRecords,
    searchRecords,
    getRecordsByDateRange,
    exportRecords,
    importRecords
  };
});
