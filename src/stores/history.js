import { defineStore } from 'pinia';
import { ref, watch } from 'vue';

const MAX_HISTORY = 20;
const STORAGE_KEY = 'bundleHistory';

export const useHistoryStore = defineStore('history', () => {
  const records = ref(JSON.parse(localStorage.getItem(STORAGE_KEY)) || []);

  watch(records, (newVal) => {
    const trimmed = newVal.slice(0, MAX_HISTORY);
    localStorage.setItem(STORAGE_KEY, JSON.stringify(trimmed));
  }, { deep: true });

  const addRecord = (input, results) => {
    records.value.unshift({
      timestamp: new Date().toISOString(),
      input: { ...input },
      results: [...results]
    });
  };

  return { records, addRecord };
});
