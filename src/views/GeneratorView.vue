<template>
  <div class="generator">
    <BundleIdForm @submit="handleSubmit" />
    <ResultCard
      v-if="results.length"
      :results="results"
      @regenerate="generateIds"
    />
    <HistoryList />
  </div>
</template>

<script setup>
import { ref } from 'vue';
import useIdGenerator from '@/composables/useIdGenerator';
import { useHistoryStore } from '@/stores/history';

const { generate } = useIdGenerator();
const historyStore = useHistoryStore();
const results = ref([]);
const currentInput = ref(null);

const handleSubmit = (input) => {
  currentInput.value = input;
  generateIds();
};

const generateIds = () => {
  results.value = generate(currentInput.value);
  historyStore.addRecord(currentInput.value, results.value);
};
</script>
