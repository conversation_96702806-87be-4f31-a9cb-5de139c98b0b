<template>
  <div class="generator-view">
    <div class="generator-container">
      <Transition name="slide-up" appear>
        <BundleIdForm @submit="handleSubmit" />
      </Transition>

      <Transition name="fade" mode="out-in">
        <ResultCard
          v-if="results.length"
          :key="results.join(',')"
          :results="results"
          @regenerate="generateIds"
        />
      </Transition>

      <Transition name="slide-up" appear>
        <HistoryList />
      </Transition>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue';
import BundleIdForm from '@/components/BundleIdForm.vue';
import ResultCard from '@/components/ResultCard.vue';
import HistoryList from '@/components/HistoryList.vue';
import useIdGenerator from '@/composables/useIdGenerator';
import { useHistoryStore } from '@/stores/history';

const { generate } = useIdGenerator();
const historyStore = useHistoryStore();
const results = ref([]);
const currentInput = ref(null);

const handleSubmit = (input) => {
  currentInput.value = input;
  generateIds();
};

const generateIds = () => {
  results.value = generate(currentInput.value);
  historyStore.addRecord(currentInput.value, results.value);
};
</script>

<style scoped>
.generator-view {
  min-height: calc(100vh - 200px);
  display: flex;
  align-items: flex-start;
  justify-content: center;
  padding: 2rem 0;
}

.generator-container {
  width: 100%;
  max-width: 800px;
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

/* Transition animations */
.slide-up-enter-active {
  transition: all 0.6s ease-out;
}

.slide-up-enter-from {
  opacity: 0;
  transform: translateY(30px);
}

.fade-enter-active,
.fade-leave-active {
  transition: all 0.4s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
  transform: scale(0.95);
}

@media (max-width: 768px) {
  .generator-view {
    padding: 1rem 0;
  }

  .generator-container {
    gap: 1.5rem;
  }
}

@media (max-width: 480px) {
  .generator-view {
    padding: 0.5rem 0;
  }

  .generator-container {
    gap: 1rem;
  }
}
</style>
