export default function useIdGenerator() {
  // Helper function to clean and format strings for bundle IDs
  const cleanString = (str) => {
    return str
      .toLowerCase()
      .replace(/[^a-z0-9]/g, '')
      .replace(/^[0-9]+/, '') // Remove leading numbers
      .slice(0, 12) // Limit length
  }

  // Helper function to generate domain-style names
  const generateDomainName = (companyName, appName) => {
    if (companyName) {
      const cleanCompany = cleanString(companyName)
      if (cleanCompany.length >= 3) {
        return cleanCompany
      }
    }

    // Fallback to app name or generic
    const cleanApp = cleanString(appName)
    return cleanApp.length >= 3 ? cleanApp : 'app'
  }

  // Helper function to generate app identifier
  const generateAppIdentifier = (appName, industry, style) => {
    const cleanApp = cleanString(appName)
    const industryMap = {
      technology: ['tech', 'dev', 'digital'],
      finance: ['fin', 'pay', 'money'],
      healthcare: ['health', 'med', 'care'],
      education: ['edu', 'learn', 'study'],
      entertainment: ['fun', 'play', 'media'],
      ecommerce: ['shop', 'store', 'buy'],
      travel: ['trip', 'go', 'travel'],
      food: ['food', 'eat', 'cook'],
      fitness: ['fit', 'gym', 'health'],
      social: ['social', 'chat', 'connect'],
      productivity: ['work', 'task', 'pro'],
      games: ['game', 'play', 'fun'],
      news: ['news', 'info', 'read'],
      business: ['biz', 'work', 'corp'],
      lifestyle: ['life', 'style', 'daily']
    }

    const styleMap = {
      marketing: ['pro', 'plus', 'max'],
      functional: ['app', 'tool', 'util'],
      formal: ['std', 'official', 'main'],
      creative: ['x', 'go', 'hub']
    }

    const industryOptions = industryMap[industry] || ['app']
    const styleOptions = styleMap[style] || ['app']

    return {
      base: cleanApp,
      industry: industryOptions,
      style: styleOptions
    }
  }

  // Helper function to generate region codes
  const generateRegionCode = (regions) => {
    const regionMap = {
      global: 'global',
      northAmerica: 'na',
      europe: 'eu',
      asia: 'asia',
      china: 'cn',
      japan: 'jp',
      southKorea: 'kr',
      southeastAsia: 'sea',
      middleEast: 'me',
      africa: 'af',
      southAmerica: 'sa',
      oceania: 'oc'
    }

    if (regions.length === 0) return 'global'
    if (regions.length === 1) return regionMap[regions[0]] || 'global'

    // For multiple regions, create combinations
    const codes = regions.map(r => regionMap[r]).filter(Boolean)
    if (codes.length <= 2) {
      return codes.join('')
    }
    return 'multi'
  }

  const generate = (input, count = 3, excludeIds = []) => {
    const { appName, industry, regions, companyName, style } = input

    if (!appName || !industry || !style) {
      return ['com.example.app', 'com.example.app2', 'com.example.app3']
    }

    const domain = generateDomainName(companyName, appName)
    const appIdentifiers = generateAppIdentifier(appName, industry, style)
    const regionCode = generateRegionCode(regions || [])

    const results = []
    const usedIds = new Set(excludeIds) // Exclude previously generated IDs

    // Generate different variations
    const variations = [
      // Standard format: com.domain.appname
      () => `com.${domain}.${appIdentifiers.base}`,

      // With industry: com.domain.appname.industry
      () => `com.${domain}.${appIdentifiers.base}.${appIdentifiers.industry[0]}`,

      // With style: com.domain.style.appname
      () => `com.${domain}.${appIdentifiers.style[0]}.${appIdentifiers.base}`,

      // With region: com.domain.appname.region
      () => regionCode !== 'global' ? `com.${domain}.${appIdentifiers.base}.${regionCode}` : null,

      // Complex: com.domain.industry.appname.style
      () => `com.${domain}.${appIdentifiers.industry[0]}.${appIdentifiers.base}.${appIdentifiers.style[0]}`,

      // Alternative industry terms
      () => `com.${domain}.${appIdentifiers.base}.${appIdentifiers.industry[1] || appIdentifiers.industry[0]}`,

      // Alternative style terms
      () => `com.${domain}.${appIdentifiers.style[1] || appIdentifiers.style[0]}.${appIdentifiers.base}`,

      // Shortened app name with industry
      () => `com.${domain}.${appIdentifiers.base.slice(0, 6)}.${appIdentifiers.industry[0]}`,

      // With multiple elements
      () => `com.${domain}.${appIdentifiers.industry[0]}.${appIdentifiers.base}.${regionCode}`,

      // Creative combinations
      () => `com.${domain}.${appIdentifiers.base}${appIdentifiers.style[0]}`,
    ]

    // Generate unique bundle IDs
    let attempts = 0
    while (results.length < count && attempts < variations.length * 3) {
      const variation = variations[attempts % variations.length]
      const bundleId = variation()

      if (bundleId && !usedIds.has(bundleId) && bundleId.length <= 100) {
        // Validate bundle ID format
        if (/^[a-z][a-z0-9]*(\.[a-z][a-z0-9]*)*$/.test(bundleId)) {
          results.push(bundleId)
          usedIds.add(bundleId)
        }
      }
      attempts++
    }

    // Fill remaining slots with numbered variations if needed
    while (results.length < count) {
      const base = results[0] || `com.${domain}.${appIdentifiers.base}`
      const numbered = `${base}${results.length + 1}`
      if (!usedIds.has(numbered)) {
        results.push(numbered)
        usedIds.add(numbered)
      } else {
        results.push(`${base}v${results.length + 1}`)
      }
    }

    return results.slice(0, count)
  }

  return { generate }
}
