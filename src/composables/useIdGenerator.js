export default function useIdGenerator() {
  const generate = (input, count = 3) => {
    const { appName, industry, regions, style } = input;
    const cleanName = appName.replace(/\s+/g, '').slice(0, 8).toLowerCase();
    const regionCode = regions.length ? regions.join('-') : 'global';
    const styleMap = {
      marketing: 'mkt',
      functional: 'func',
      formal: 'std',
      creative: 'crv'
    };

    return Array(count).fill().map((_, i) => {
      return `com.${cleanName}.${industry.slice(0, 3)}.${regionCode}.${styleMap[style] || 'gen'}${i + 1}`;
    });
  }
  return { generate };
}
