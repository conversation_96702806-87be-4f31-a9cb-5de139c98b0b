<template>
  <div class="bundle-form">
    <div class="form-header">
      <h2>{{ $t('app.title') }}</h2>
      <p class="subtitle">{{ $t('app.subtitle') }}</p>
    </div>

    <form @submit.prevent="handleSubmit" class="form-container">
      <!-- App Name -->
      <div class="form-group">
        <label for="appName" class="form-label">
          {{ $t('form.appName') }} <span class="required">*</span>
        </label>
        <input
          id="appName"
          v-model="formData.appName"
          type="text"
          class="form-input"
          :placeholder="$t('form.appNamePlaceholder')"
          :class="{ 'error': errors.appName }"
        />
        <span v-if="errors.appName" class="error-message">{{ errors.appName }}</span>
      </div>

      <!-- Industry -->
      <div class="form-group">
        <label for="industry" class="form-label">
          {{ $t('form.industry') }} <span class="required">*</span>
        </label>
        <select
          id="industry"
          v-model="formData.industry"
          class="form-select"
          :class="{ 'error': errors.industry }"
        >
          <option value="">{{ $t('form.industryPlaceholder') }}</option>
          <option v-for="(label, key) in industries" :key="key" :value="key">
            {{ label }}
          </option>
        </select>
        <span v-if="errors.industry" class="error-message">{{ errors.industry }}</span>
      </div>

      <!-- Regions -->
      <div class="form-group">
        <label class="form-label">
          {{ $t('form.regions') }} <span class="required">*</span>
        </label>
        <div class="checkbox-group">
          <label v-for="(label, key) in regions" :key="key" class="checkbox-label">
            <input
              type="checkbox"
              :value="key"
              v-model="formData.regions"
              class="checkbox-input"
            />
            <span class="checkbox-text">{{ label }}</span>
          </label>
        </div>
        <span v-if="errors.regions" class="error-message">{{ errors.regions }}</span>
      </div>

      <!-- Company Name (Optional) -->
      <div class="form-group">
        <label for="companyName" class="form-label">
          {{ $t('form.companyName') }}
        </label>
        <input
          id="companyName"
          v-model="formData.companyName"
          type="text"
          class="form-input"
          :placeholder="$t('form.companyNamePlaceholder')"
        />
      </div>

      <!-- Style Preference -->
      <div class="form-group">
        <label for="style" class="form-label">
          {{ $t('form.style') }} <span class="required">*</span>
        </label>
        <select
          id="style"
          v-model="formData.style"
          class="form-select"
          :class="{ 'error': errors.style }"
        >
          <option value="">{{ $t('form.stylePlaceholder') }}</option>
          <option v-for="(label, key) in styles" :key="key" :value="key">
            {{ label }}
          </option>
        </select>
        <span v-if="errors.style" class="error-message">{{ errors.style }}</span>
      </div>

      <!-- Submit Button -->
      <button type="submit" class="submit-button" :disabled="isSubmitting">
        {{ isSubmitting ? '...' : $t('form.generate') }}
      </button>
    </form>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue'
import { useI18n } from 'vue-i18n'

const { t } = useI18n()

const emit = defineEmits(['submit'])

const formData = ref({
  appName: '',
  industry: '',
  regions: [],
  companyName: '',
  style: ''
})

const errors = ref({})
const isSubmitting = ref(false)

// Computed properties for translated options
const industries = computed(() => {
  const industryKeys = ['technology', 'finance', 'healthcare', 'education', 'entertainment', 
                       'ecommerce', 'travel', 'food', 'fitness', 'social', 'productivity', 
                       'games', 'news', 'business', 'lifestyle']
  return industryKeys.reduce((acc, key) => {
    acc[key] = t(`industries.${key}`)
    return acc
  }, {})
})

const regions = computed(() => {
  const regionKeys = ['global', 'northAmerica', 'europe', 'asia', 'china', 'japan', 
                     'southKorea', 'southeastAsia', 'middleEast', 'africa', 'southAmerica', 'oceania']
  return regionKeys.reduce((acc, key) => {
    acc[key] = t(`regions.${key}`)
    return acc
  }, {})
})

const styles = computed(() => {
  const styleKeys = ['marketing', 'functional', 'formal', 'creative']
  return styleKeys.reduce((acc, key) => {
    acc[key] = t(`styles.${key}`)
    return acc
  }, {})
})

// Validation
const validateForm = () => {
  errors.value = {}
  
  if (!formData.value.appName.trim()) {
    errors.value.appName = t('validation.appNameRequired')
  }
  
  if (!formData.value.industry) {
    errors.value.industry = t('validation.industryRequired')
  }
  
  if (formData.value.regions.length === 0) {
    errors.value.regions = t('validation.regionsRequired')
  }
  
  if (!formData.value.style) {
    errors.value.style = t('validation.styleRequired')
  }
  
  return Object.keys(errors.value).length === 0
}

const handleSubmit = async () => {
  if (!validateForm()) {
    return
  }
  
  isSubmitting.value = true
  
  try {
    emit('submit', { ...formData.value })
  } finally {
    isSubmitting.value = false
  }
}
</script>

<style scoped>
.bundle-form {
  max-width: 600px;
  margin: 0 auto;
  padding: 2rem;
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.form-header {
  text-align: center;
  margin-bottom: 2rem;
}

.form-header h2 {
  color: #2c3e50;
  margin-bottom: 0.5rem;
  font-size: 2rem;
  font-weight: 600;
}

.subtitle {
  color: #6c757d;
  font-size: 1rem;
  margin: 0;
}

.form-container {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.form-group {
  display: flex;
  flex-direction: column;
}

.form-label {
  font-weight: 500;
  margin-bottom: 0.5rem;
  color: #374151;
  font-size: 0.875rem;
}

.required {
  color: #ef4444;
}

.form-input,
.form-select {
  padding: 0.75rem;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  font-size: 1rem;
  transition: border-color 0.2s ease, box-shadow 0.2s ease;
}

.form-input:focus,
.form-select:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.form-input.error,
.form-select.error {
  border-color: #ef4444;
}

.checkbox-group {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 0.75rem;
  margin-top: 0.5rem;
}

.checkbox-label {
  display: flex;
  align-items: center;
  cursor: pointer;
  padding: 0.5rem;
  border-radius: 4px;
  transition: background-color 0.2s ease;
}

.checkbox-label:hover {
  background-color: #f9fafb;
}

.checkbox-input {
  margin-right: 0.5rem;
}

.checkbox-text {
  font-size: 0.875rem;
  color: #374151;
}

.error-message {
  color: #ef4444;
  font-size: 0.75rem;
  margin-top: 0.25rem;
}

.submit-button {
  background-color: #3b82f6;
  color: white;
  padding: 0.875rem 1.5rem;
  border: none;
  border-radius: 6px;
  font-size: 1rem;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s ease;
  margin-top: 1rem;
}

.submit-button:hover:not(:disabled) {
  background-color: #2563eb;
}

.submit-button:disabled {
  background-color: #9ca3af;
  cursor: not-allowed;
}

@media (max-width: 768px) {
  .bundle-form {
    padding: 1rem;
    margin: 1rem;
  }
  
  .checkbox-group {
    grid-template-columns: 1fr;
  }
}
</style>
