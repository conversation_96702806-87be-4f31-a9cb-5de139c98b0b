<template>
  <div class="result-card">
    <div class="result-header">
      <h3>{{ $t('results.title') }}</h3>
      <button @click="handleRegenerate" class="regenerate-button">
        {{ $t('results.regenerate') }}
      </button>
    </div>
    
    <div class="results-list">
      <div 
        v-for="(result, index) in results" 
        :key="index" 
        class="result-item"
      >
        <div class="result-content">
          <code class="bundle-id">{{ result }}</code>
          <button 
            @click="copyToClipboard(result, index)" 
            class="copy-button"
            :class="{ 'copied': copiedIndex === index }"
          >
            {{ copiedIndex === index ? $t('results.copied') : $t('results.copy') }}
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import { useI18n } from 'vue-i18n'

const { t } = useI18n()

const props = defineProps({
  results: {
    type: Array,
    required: true
  }
})

const emit = defineEmits(['regenerate'])

const copiedIndex = ref(-1)

const copyToClipboard = async (text, index) => {
  try {
    await navigator.clipboard.writeText(text)
    copiedIndex.value = index
    setTimeout(() => {
      copiedIndex.value = -1
    }, 2000)
  } catch (err) {
    console.error('Failed to copy text: ', err)
    // Fallback for older browsers
    const textArea = document.createElement('textarea')
    textArea.value = text
    document.body.appendChild(textArea)
    textArea.select()
    document.execCommand('copy')
    document.body.removeChild(textArea)
    
    copiedIndex.value = index
    setTimeout(() => {
      copiedIndex.value = -1
    }, 2000)
  }
}

const handleRegenerate = () => {
  emit('regenerate')
}
</script>

<style scoped>
.result-card {
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  padding: 1.5rem;
  margin-top: 2rem;
}

.result-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
  padding-bottom: 1rem;
  border-bottom: 1px solid #e5e7eb;
}

.result-header h3 {
  color: #2c3e50;
  margin: 0;
  font-size: 1.25rem;
  font-weight: 600;
}

.regenerate-button {
  background-color: #10b981;
  color: white;
  padding: 0.5rem 1rem;
  border: none;
  border-radius: 6px;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.regenerate-button:hover {
  background-color: #059669;
}

.results-list {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.result-item {
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  overflow: hidden;
  transition: border-color 0.2s ease;
}

.result-item:hover {
  border-color: #3b82f6;
}

.result-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem;
  background-color: #f9fafb;
}

.bundle-id {
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 0.875rem;
  color: #1f2937;
  background-color: #f3f4f6;
  padding: 0.5rem 0.75rem;
  border-radius: 4px;
  border: 1px solid #d1d5db;
  flex: 1;
  margin-right: 1rem;
  word-break: break-all;
}

.copy-button {
  background-color: #3b82f6;
  color: white;
  padding: 0.5rem 1rem;
  border: none;
  border-radius: 4px;
  font-size: 0.75rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  min-width: 80px;
}

.copy-button:hover {
  background-color: #2563eb;
}

.copy-button.copied {
  background-color: #10b981;
}

.copy-button.copied:hover {
  background-color: #059669;
}

@media (max-width: 768px) {
  .result-card {
    margin: 1rem;
    padding: 1rem;
  }
  
  .result-header {
    flex-direction: column;
    gap: 1rem;
    align-items: stretch;
  }
  
  .result-content {
    flex-direction: column;
    gap: 0.75rem;
    align-items: stretch;
  }
  
  .bundle-id {
    margin-right: 0;
    text-align: center;
  }
  
  .copy-button {
    width: 100%;
  }
}
</style>
