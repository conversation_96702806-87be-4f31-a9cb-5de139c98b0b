<template>
  <div class="language-switcher">
    <label for="language-select" class="sr-only">{{ $t('language.switch') }}</label>
    <select
      id="language-select"
      v-model="locale"
      class="language-select"
      :title="$t('language.switch')"
    >
      <option value="en">{{ $t('language.english') }}</option>
      <option value="zh">{{ $t('language.chinese') }}</option>
    </select>
  </div>
</template>

<script setup>
import { useI18n } from 'vue-i18n';
import { watch } from 'vue';

const { locale } = useI18n();

watch(locale, (val) => {
  localStorage.setItem('userLang', val);
});
</script>

<style scoped>
.language-switcher {
  position: relative;
}

.language-select {
  padding: 8px 12px;
  border: 1px solid #e1e5e9;
  border-radius: 6px;
  background-color: white;
  font-size: 14px;
  cursor: pointer;
  transition: border-color 0.2s ease;
}

.language-select:hover {
  border-color: #007bff;
}

.language-select:focus {
  outline: none;
  border-color: #007bff;
  box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
}

.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}
</style>
