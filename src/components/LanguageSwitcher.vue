<template>
  <div class="language-switcher">
    <label for="language-select" class="sr-only">{{ $t('language.switch') }}</label>
    <select
      id="language-select"
      v-model="locale"
      class="language-select"
      :title="$t('language.switch')"
    >
      <option value="en">{{ $t('language.english') }}</option>
      <option value="zh">{{ $t('language.chinese') }}</option>
      <option value="es">{{ $t('language.spanish') }}</option>
      <option value="fr">{{ $t('language.french') }}</option>
      <option value="de">{{ $t('language.german') }}</option>
      <option value="ja">{{ $t('language.japanese') }}</option>
    </select>
  </div>
</template>

<script setup>
import { useI18n } from 'vue-i18n';
import { watch } from 'vue';

const { locale } = useI18n();

watch(locale, (val) => {
  localStorage.setItem('userLang', val);
});
</script>

<style scoped>
.language-switcher {
  position: relative;
}

.language-select {
  padding: 8px 12px;
  border: 1px solid rgba(71, 85, 105, 0.5);
  border-radius: 8px;
  background: rgba(51, 65, 85, 0.8);
  color: #f8fafc;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.language-select:hover {
  border-color: #3b82f6;
  background: rgba(51, 65, 85, 0.9);
}

.language-select:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.25);
}

.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}
</style>
