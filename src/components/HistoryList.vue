<template>
  <div class="history-container" v-if="historyStore.records.length > 0">
    <div class="history-header">
      <h3>{{ $t('history.title') }}</h3>
      <button @click="clearHistory" class="clear-button">
        {{ $t('history.clear') }}
      </button>
    </div>
    
    <div class="history-list">
      <div 
        v-for="(record, index) in historyStore.records" 
        :key="index"
        class="history-item"
      >
        <div class="history-item-header">
          <div class="history-meta">
            <span class="app-name">{{ record.input.appName }}</span>
            <span class="timestamp">
              {{ $t('history.generatedAt') }}: {{ formatDate(record.timestamp) }}
            </span>
          </div>
          <button 
            @click="toggleDetails(index)"
            class="toggle-button"
          >
            {{ expandedItems.includes(index) ? '−' : '+' }}
          </button>
        </div>
        
        <div v-if="expandedItems.includes(index)" class="history-details">
          <div class="input-summary">
            <div class="input-item">
              <strong>{{ $t('form.industry') }}:</strong> 
              {{ $t(`industries.${record.input.industry}`) }}
            </div>
            <div class="input-item">
              <strong>{{ $t('form.regions') }}:</strong>
              {{ record.input.regions.map(r => $t(`regions.${r}`)).join(', ') }}
            </div>
            <div class="input-item">
              <strong>{{ $t('form.style') }}:</strong>
              {{ $t(`styles.${record.input.style}`) }}
            </div>
            <div v-if="record.input.companyName" class="input-item">
              <strong>{{ $t('form.companyName') }}:</strong>
              {{ record.input.companyName }}
            </div>
          </div>
          
          <div class="results-summary">
            <h4>{{ $t('results.title') }}:</h4>
            <div class="result-list">
              <div 
                v-for="(result, resultIndex) in record.results" 
                :key="resultIndex"
                class="result-item"
              >
                <code class="bundle-id">{{ result }}</code>
                <button 
                  @click="copyToClipboard(result, `${index}-${resultIndex}`)"
                  class="copy-button"
                  :class="{ 'copied': copiedId === `${index}-${resultIndex}` }"
                >
                  {{ copiedId === `${index}-${resultIndex}` ? $t('results.copied') : $t('results.copy') }}
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
  
  <div v-else class="no-history">
    <p>{{ $t('history.noHistory') }}</p>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import { useI18n } from 'vue-i18n'
import { useHistoryStore } from '@/stores/history'

const { t } = useI18n()
const historyStore = useHistoryStore()

const expandedItems = ref([])
const copiedId = ref('')

const toggleDetails = (index) => {
  const idx = expandedItems.value.indexOf(index)
  if (idx > -1) {
    expandedItems.value.splice(idx, 1)
  } else {
    expandedItems.value.push(index)
  }
}

const clearHistory = () => {
  if (confirm('Are you sure you want to clear all history?')) {
    historyStore.records.splice(0)
    expandedItems.value = []
  }
}

const formatDate = (timestamp) => {
  return new Date(timestamp).toLocaleString()
}

const copyToClipboard = async (text, id) => {
  try {
    await navigator.clipboard.writeText(text)
    copiedId.value = id
    setTimeout(() => {
      copiedId.value = ''
    }, 2000)
  } catch (err) {
    console.error('Failed to copy text: ', err)
    // Fallback for older browsers
    const textArea = document.createElement('textarea')
    textArea.value = text
    document.body.appendChild(textArea)
    textArea.select()
    document.execCommand('copy')
    document.body.removeChild(textArea)
    
    copiedId.value = id
    setTimeout(() => {
      copiedId.value = ''
    }, 2000)
  }
}
</script>

<style scoped>
.history-container {
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  padding: 1.5rem;
  margin-top: 2rem;
}

.history-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
  padding-bottom: 1rem;
  border-bottom: 1px solid #e5e7eb;
}

.history-header h3 {
  color: #2c3e50;
  margin: 0;
  font-size: 1.25rem;
  font-weight: 600;
}

.clear-button {
  background-color: #ef4444;
  color: white;
  padding: 0.5rem 1rem;
  border: none;
  border-radius: 6px;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.clear-button:hover {
  background-color: #dc2626;
}

.history-list {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.history-item {
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  overflow: hidden;
}

.history-item-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem;
  background-color: #f9fafb;
  cursor: pointer;
}

.history-meta {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.app-name {
  font-weight: 600;
  color: #1f2937;
}

.timestamp {
  font-size: 0.75rem;
  color: #6b7280;
}

.toggle-button {
  background: none;
  border: 1px solid #d1d5db;
  border-radius: 4px;
  width: 2rem;
  height: 2rem;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  font-weight: bold;
  color: #374151;
  transition: all 0.2s ease;
}

.toggle-button:hover {
  background-color: #f3f4f6;
  border-color: #9ca3af;
}

.history-details {
  padding: 1rem;
  border-top: 1px solid #e5e7eb;
  background-color: white;
}

.input-summary {
  margin-bottom: 1rem;
}

.input-item {
  margin-bottom: 0.5rem;
  font-size: 0.875rem;
  color: #374151;
}

.results-summary h4 {
  margin: 0 0 0.75rem 0;
  color: #1f2937;
  font-size: 1rem;
}

.result-list {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.result-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.75rem;
  background-color: #f9fafb;
  border-radius: 6px;
  border: 1px solid #e5e7eb;
}

.bundle-id {
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 0.75rem;
  color: #1f2937;
  background-color: #f3f4f6;
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  border: 1px solid #d1d5db;
  flex: 1;
  margin-right: 0.75rem;
  word-break: break-all;
}

.copy-button {
  background-color: #3b82f6;
  color: white;
  padding: 0.25rem 0.75rem;
  border: none;
  border-radius: 4px;
  font-size: 0.75rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  min-width: 60px;
}

.copy-button:hover {
  background-color: #2563eb;
}

.copy-button.copied {
  background-color: #10b981;
}

.copy-button.copied:hover {
  background-color: #059669;
}

.no-history {
  text-align: center;
  padding: 2rem;
  color: #6b7280;
  font-style: italic;
}

@media (max-width: 768px) {
  .history-container {
    margin: 1rem;
    padding: 1rem;
  }
  
  .history-header {
    flex-direction: column;
    gap: 1rem;
    align-items: stretch;
  }
  
  .result-item {
    flex-direction: column;
    gap: 0.5rem;
    align-items: stretch;
  }
  
  .bundle-id {
    margin-right: 0;
    text-align: center;
  }
  
  .copy-button {
    width: 100%;
  }
}
</style>
